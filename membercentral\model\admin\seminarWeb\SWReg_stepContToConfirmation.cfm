<cfoutput>
<div class="card card-box mt-2" id="divStepContToConfirmation">
	<div class="card-header bg-light">
		<div class="card-header--title font-weight-bold font-size-md">Continue to Confirmation</div>
	</div>
	<div class="card-body">
		<cfif NOT local.stopReg>
			<div>
				<button type="button" name="btnContToConfirmation" id="btnContToConfirmation" class="btn btn-sm btn-primary" onclick="loadConfirmation();" style="width:180px;">
					<span class="btn-wrapper--label">Next Page</span>
					<span class="btn-wrapper--icon"><i class="fa-light fa-right"></i></span>
				</button>
				<span class="ml-2">Continue to verify this registration details.</span>
			</div>
		</cfif>
		<div class="mt-2">
			<button type="button" name="btnCancelConfToTicket" id="btnCancelReg" class="btn btn-sm btn-secondary" style="width:180px;"><i class="fa-solid fa-circle-minus fa-lg"></i> Cancel</button>
			<span class="ml-2">Cancel this registration and return to list of registrants.</span>
		</div>
	</div>
</div>

<div id="divStepConfirmation"></div>

<cfif local.qryAssociation.handlesOwnPayment is 0>
	<div id="divStepSWPayment"></div>
</cfif>
</cfoutput>
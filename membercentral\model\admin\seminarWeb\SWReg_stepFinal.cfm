<cfoutput>
<div class="card card-box mt-2" id="divStepFinal">
	<div class="card-header bg-light">
		<div class="card-header--title font-weight-bold font-size-md">Save Registration</div>
	</div>
	<div class="card-body">
		<div>
			<div id="err_conf_emails" class="alert alert-danger mb-2 d-none"></div>
			<span>Send registration confirmations below.</span>
			<div class="form-group row mt-2 pl-3">
				<div class="col-auto col-form-label-sm font-size-md">
					<div class="form-check">
						<input type="checkbox" name="chkEmailRegistrant" id="chkEmailRegistrant1" class="form-check-input" value="1" <cfif local.chkEmailRegistrant>checked</cfif>>
						<label for="chkEmailRegistrant1" class="form-check-label" style="width:230px;">E-mail <b>registrant confirmation</b> to </label>
					</div>
				</div>
				<div class="col">
					<input type="text" name="txtEmailRegistrant1" id="txtEmailRegistrant1" class="form-control form-control-sm ml-2" value="#local.qryCurrentRegMember.email#">
				</div>
			</div>
		</div>
		<div class="mt-3">
			<div>
				<cfif local.qryAssociation.handlesOwnPayment is 0>
					<button type="button" name="btnContinueToPayment" id="btnContinueToPayment" class="btn btn-sm btn-primary" onClick="loadSWPaymentForm();" style="width:180px;">
						<span class="btn-wrapper--icon"><i class="fa fa-circle-plus"></i></span>
						<span class="btn-wrapper--label">Continue</span>
					</button>
					<span class="ml-2">Continue to payment</span>
				<cfelse>
					<button type="button" name="btnSubmit" id="btnSubmitSave" class="btn btn-sm btn-primary" onclick="toggleSaveBtn(false);submitSWRegForm();" style="width:180px;">
						<span class="btn-wrapper--icon"><i class="fa fa-circle-plus"></i></span>
						<span class="btn-wrapper--label">Save/Continue</span>
					</button>
					<span class="ml-2">Save registration</span>
				</cfif>
			</div>
			<div class="mt-2">
				<button type="button" name="btnChangePrice" id="btnChangePrice" class="btn btn-sm btn-secondary" onclick="showChangePriceOptions();" style="width:180px;">
					<span class="btn-wrapper--icon"><i class="fa fa-money-bill"></i></span>
					<span class="btn-wrapper--label">Change Prices</span>
				</button>
				<span class="ml-2">Change prices for this registration.</span>
			</div>
			<div class="mt-2">
				<button type="button" name="btnCancel" id="btnCancelReg" class="btn btn-sm btn-secondary" style="width:180px;">
					<span class="btn-wrapper--icon"><i class="fa fa-circle-minus"></i></span>
					<span class="btn-wrapper--label">Cancel Changes</span>
				</button>
				<span class="ml-2">Cancel this registration and return to list of registrants.</span>
			</div>
		</div>
	</div>
</div>
</cfoutput>
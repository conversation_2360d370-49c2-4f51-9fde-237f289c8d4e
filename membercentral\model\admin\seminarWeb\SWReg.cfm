<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>
<cfsavecontent variable="local.swhead">
	<style type="text/css">
		.mcswreg_strike { text-decoration:line-through; }
		.mcswreg_highlight { font-weight:bold; font-size:15px; }
		.mcswreg_redtext { color:red; }
		#mcswreg_item_info { color:green; font-weight:bold; }
	</style>
	<cfoutput>
	<script type="text/javascript" src="/assets/common/javascript/resourceFields.js#local.assetCachingKey#"></script>
	<script language="javascript">
		function closeBox() { MCModalUtils.hideModal(); }	
		function cancelReg() {
			top.MCModalUtils.hideModal();
		}
		function initCancelRegButton() {
			let cancelBtn = $('##btnCancelReg');
			if (cancelBtn.length) {
				mca_initConfirmButton(cancelBtn, function(){
					cancelReg();
				},
				'<span class="btn-wrapper--icon"><i class="fa fa-circle-minus"></i></span><span class="btn-wrapper--label">Cancel Changes</span>',
				'<i class="fa-solid fa-circle-info text-danger"></i> Click to Confirm'
				);
			}
		}
		function toggleSaveBtn(f) {
			$('button##btnSubmitSave').prop('disabled',!f);
		}
		function submitSWRegForm() {
			let arrReq = [];
			toggleSaveBtn(false);
			<cfif local.qryAssociation.handlesOwnPayment is 0>
				let totalRegFee = getTotalRegFee();
				if (totalRegFee > 0) {
					let SWRegPaymentMethod = $('##SWRegPaymentMethod').val().toLowerCase();
					if (SWRegPaymentMethod == 'cc' && $('##p_SW_DIV').length && $('##p_SW_DIV').attr("pofcount") == 0) {
						arrReq.push('Enter your credit card information to continue.');
					} else if (SWRegPaymentMethod == 'offline' && !$('##offlinePaymentDesc').val().trim().length) {
						arrReq.push('Enter the Offline payment description. This usually includes the Check Number or Last 4 Digits of Card.');
					} else if (SWRegPaymentMethod == 'offline' && !$('##tspaymentMethod').val().trim().length) {
						arrReq.push('Select the Payment Method.');
					}
				}
			</cfif>
			if (arrReq.length) {
				showAlert(arrReq.join('<br/>'));
				toggleSaveBtn(true);
				return false;
			}
			$('form##frmRegistrant').submit();
		}
		function showChangePriceOptions() {
			$('div##divStepFinal, tr.MCDispRegSummaryItem').hide(300);
			$('tr.MCRegPriceItem, div##divStepConfirmPriceChange').show(300);
			hideAppliedCouponInfo();
		}
		function calculateTotalRegFee() {
			var total = getTotalRegFee();
			
			$('b##MCTotalRegFee').html('$' + total.toFixed(2) + $('b##MCTotalRegFee').data('defaultcurrencytype'));
			$('##mcsw_currentPrice').val(total);
		}
		function getTotalRegFee() {
			var total = 0;
			var currentPrice = Number(formatCurrency($('##mcsw_currentPrice').val()).replace(',',''));
			var newPrice = Number(formatCurrency($('##override_swRatePrice').val()).replace(',',''));

			if (newPrice != currentPrice) total = newPrice;
			else total = currentPrice;

			return total;
		}
		function saveChangePrices() {
			var defaultcurrencytype = $('b##MCTotalRegFee').data('defaultcurrencytype');
			
			$('.MCRegPriceItem').find('input[data-israte="1"]').each(function() {
				var linkedDispColID = $(this).data('linkeddispcolumnid');
				var totalAmt = Number(formatCurrency(this.value).replace(',',''));
				$('##'+linkedDispColID).html('$' + totalAmt.toFixed(2) + defaultcurrencytype);
			});

			if($('##mcsw_couponID').length && Number($('##mcsw_couponID').val()) > 0) {
				recalculateDiscount($('##mcsw_couponID').val());
			}

			$('tr.MCRegPriceItem, div##divStepConfirmPriceChange').hide();
			$('div##divStepFinal, tr.MCDispRegSummaryItem').show(300);

			$('html, body').animate({
				scrollTop: $('div##divStepFinal').offset().top - 250
			}, 300);
		}
		function checkIDNum(cbox,crreq) {
			if (crreq == 1) cbox.checked = true;
			if (cbox.checked) showIDNum(cbox.value); else hideIDNum(cbox.value); 
		};
		function showIDNum(lid) { document.getElementById('dividnum' + lid).style.display = ''; };
		function hideIDNum(lid) { document.getElementById('dividnum' + lid).style.display = 'none'; };
		function showChild(obj) {
			if (obj.data("value") == 0) { // Select All
				obj.text("Unselect All");
				obj.data("value", 1); // Set using data()
				$(".creditcbox").each(function () {
					if (!$(this).prop("checked")) {
						$(this).trigger('click'); // Check and trigger click
					}
				});
			} else { // Unselect All
				obj.text("Select All");
				obj.data("value", 0); // Set using data()
				$(".creditcbox").each(function () {
					if ($(this).prop("checked")) {
						$(this).trigger('click'); // Uncheck and trigger click
					}
				});
			}
		}
		function copyDown(obj,aID){		
			$(".frmTxt"+aID).val(obj.val());
		}	
		function showCopyDown(sid){
			if($("##scid_"+sid).val().trim().length > 0){
				$("##frmlink"+sid+"ID").show();
			}
			else {
				$("##frmlink"+sid+"ID").hide();
			}	
		}

		$(function() {
			$('.creditcbox').click(function() {
				if ($('.creditcbox:checked').length == $('.creditcbox').length) {
					$("##parent").text('Unselect All');
					$("##parent").data("value",1);
				} else {
					$("##parent").text('Select All');
					$("##parent").data("value",0);
				}
			});
				if (top.MCModalUtils.isShown()) {
					top.$('##MCModalLabel').html('#encodeForJavascript(#arguments.qryProgram.programName#)# <cfif len(arguments.qryProgram.programSubTitle)> (#encodeForJavascript(arguments.qryProgram.programSubTitle)#)</cfif>');
				}
			initCancelRegButton();
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.swhead#">

<cfoutput>

<div class="p-2">
	<!--- error placeholder --->
	<div id="everr" class="alert alert-danger mb-2 d-none"></div>

	<!--- display registrant selector --->
	<cfinclude template="SWReg_step1.cfm">

	<cfif local.qryCurrentRegMember.recordCount>
		<cfset local.jsValidation = "">
		<cfset local.stopReg = false>

		<div id="divSteps12">
			<!--- step 2 (rate and credit information) --->
			<cfinclude template="SWReg_step2.cfm">
		</div>
		<!--- step confirmation (verify) --->
		<cfinclude template="SWReg_stepContToConfirmation.cfm">
		</form>

		<cfsavecontent variable="local.regvalidate">
			<cfoutput>
			<cfif listFindNoCase("SWL,SWOD",arguments.programType) AND local.strProgramRegFields.hasFields>
				#local.strProgramRegFields.head#
			</cfif>
			<script language="javascript">
				hideAlert = function() { mca_hideAlert('everr'); };
				showAlert = function(msg) { mca_showAlert('everr', msg, true); };

				function doS2Validate() {
					arrReq = new Array();

					<cfif local.qryAssociation.handlesOwnPayment is 1>
						let stateIDforTax = $('##stateIDforTax').val();
						let billingstate = '';
						if (stateIDforTax == '') arrReq[arrReq.length] = '#JSStringFormat('Billing State/Province is required.')#';
					<cfelse>
						let stateIDforTax = 0;
						let billingstate = $('##billingstate').val();
						if (billingstate == '') arrReq[arrReq.length] = '#JSStringFormat('Billing State/Province is required.')#';
					</cfif>
					let zipForTax = $('##zipForTax').val();
					if (zipForTax == '') arrReq[arrReq.length] = '#JSStringFormat('Billing Postal Code is required.')#';

					if ((stateIDforTax > 0 || billingstate.length) && zipForTax.length && !mc_isValidBillingZip(zipForTax,stateIDforTax,billingstate))
						arrReq[arrReq.length] = 'Invalid Billing Postal Code.';
					
					#local.jsValidation#

					if ($('##chkEmailRegistrant1').is(':checked') && $('##txtEmailRegistrant1').val().length == 0) 
						$('##chkEmailRegistrant1').attr('checked', false);
					
					<cfif listFindNoCase("SWL,SWOD",arguments.programType) AND local.strProgramRegFields.hasFields>
						var errorMsgArray = [];
						#local.strProgramRegFields.jsValidation#
						/*drop empty elements*/
						var finalErrors = $.map(errorMsgArray, function(thisError){
							if (thisError.length) return thisError;
							else return null;
						});
						if(finalErrors.length)
							arrReq = arrReq.concat(finalErrors);
					</cfif>

					<cfif arguments.qryProgram.offerCredit is 1>
						var missingID = false;
						var err = '';
						<cfset local.reqCreditList = valueList(local.qryIDRequired.seminarCreditID)>
						<cfloop query="local.qryCredit">
							if ($('##frmcreditlink#local.qryCredit.seminarCreditID#').is(':checked')) {
								if ($('##scid_#local.qryCredit.seminarCreditID#').length && $('##scid_#local.qryCredit.seminarCreditID#').val().length == 0 && #ListContains("#local.reqCreditList#",#local.qryCredit.seminarCreditID#) GT 0#) {
									missingID = true;
									err = "#JSStringFormat('Enter the required ID number for #local.qryCredit.seminarName#.')#";
								}
							}
						</cfloop>
						if (missingID) arrReq[arrReq.length] = err;
					</cfif>

					if (arrReq.length > 0) {
						showAlert(arrReq.join('<br/>'));
						toggleSaveBtn(true);
						return false;
					} else {
						hideAlert();
						return true;
					}
				}
				function loadConfirmation() {
					if (doS2Validate()) {
						$('div##divSteps12, div.regTaxInfo, div##divStep1Date, div##divStepContToConfirmation').hide();
						
						var fd = new Object();
							fd.siteid = #arguments.event.getValue('mc_siteInfo.siteid')#;
							fd.mid = #arguments.event.getValue('mid')#;
							fd.pid = #arguments.event.getValue('pid')#;

						var frmRegistrantElements = $('##frmRegistrant input[type="hidden"], ##frmRegistrant input[type="text"], ##frmRegistrant select, ##frmRegistrant textarea, ##frmRegistrant input[type="radio"]:checked, ##frmRegistrant input[type="checkbox"]:checked');

						frmRegistrantElements.each(function() { 
							if (fd[this.name] === undefined) {
								var selval = '';
								if (this.type == 'checkbox')
									selval = $('input[name="'+this.name+'"]:checked').map(function(){ return $(this).val(); }).get().toString();
								else if (this.type == 'file')
									selval = $(this).val().length > 0 ? $(this)[0].files[0].name : '';
								else 
									selval = $(this).val() || '' ? $(this).val().toString() : '';
								
								if (selval != '') fd[this.name] = selval;
							}
						});

						$('div##divStepConfirmation')
							.html('<div class="mt-4 text-center"><div class="spinner-border" role="status"></div><div class="mt-2"><span class="font-weight-bold">Please Wait...</span><br/>We\'re loading the next page for you.</div></div>')
							.show()
							.load('#arguments.event.getValue('loadConfirmationurl')#',fd, function(){
								mca_setupTagsInput('txtEmailRegistrant1', 'err_conf_emails', "#application.regEx.email#", 'email address');
							});

						return false;
					}
				}
				<cfif local.qryAssociation.handlesOwnPayment is 0>
					function loadSWPaymentForm() {
						$('div##divStepFinal, div##divStepConfirmation').hide(300);

						var fd = new Object();
						fd.siteid = #arguments.event.getValue('mc_siteInfo.siteid')#;
						fd.mid = #arguments.event.getValue('mid')#;
						fd.did = #local.qryDepoMemberData.depomemberdataid#;
						fd.pid = #arguments.event.getValue('pid')#;
						fd.billingState = $('##billingState').val();
						fd.sw_rate  = $('input[name="sw_rate"]:checked').val();
						fd.totalRegFee = $('##mcsw_currentPrice').val();

						if($('##mcsw_couponID').length && Number($('##mcsw_couponID').val()) > 0) {
							fd.couponID = Number($('##mcsw_couponID').val());
							fd.couponDiscount = Number($('##mcsw_discount').val());
						}

						$('div##divStepSWPayment')
							.html('<div class="mt-4 text-center"><div class="spinner-border" role="status"></div><div class="mt-2"><span class="font-weight-bold">Please Wait...</span><br/>We\'re loading the next page for you.</div></div>')
							.show()
							.load('#arguments.event.getValue('loadpaymenturl')#',fd);

						return false;
					}
				</cfif>
				function validateCouponCode() {
					var responseContainerClasses = 'alert alert-danger mb-0 mt-2';
					var validateResult = function(r) {
						toggleApplyCouponButton(true);
						$('##couponCode').val('');
						if (r.success && r.success.toLowerCase() == 'true') {
							if (r.isvalidcoupon.toString().toLowerCase() == 'true') {
								var defaultcurrencytype = $('b##MCTotalRegFee').data('defaultcurrencytype');

								var totalRegFee = getTotalRegFee();	
								var discountAppliedTotal = totalRegFee - r.discount;

								var priceBox = '<span class="mcswreg_strike">$'+totalRegFee.toFixed(2)+defaultcurrencytype+'</span>&nbsp;&nbsp;<span class="mcswreg_highlight">$'+discountAppliedTotal.toFixed(2)+defaultcurrencytype+'</span><br/><span class="mcswreg_redtext">$'+r.discount+defaultcurrencytype+' discount applied</span>';
								if(!r.isavailable)
									r.couponresponse += '<br/>(This coupon is outside the allowed date range, but has been applied anyway)';

								$('##MCTotalRegFee').html(priceBox);
								$('##mcswreg_couponbox').hide();
								$('##mcswreg_item_info').html(r.couponresponse).show();
								$('##btnRemoveCoupon').parent().show();

								$('##mcsw_couponID').val(r.couponid);
								$('##mcsw_discount').val(r.discount);
							} else {
								$('##couponCodeResponse').addClass(responseContainerClasses).html(r.couponresponse);
							}
							
						} else if (r.success && r.success.toLowerCase() == 'false' && r.couponresponse) {
							$('##couponCodeResponse').addClass(responseContainerClasses).html(r.couponresponse);
						} else {
							$('##couponCodeResponse').addClass(responseContainerClasses).html('Unable to apply coupon code. Try again.');
						}
					};

					var couponCode = $('##couponCode').val().trim();

					if (couponCode.length) {
						toggleApplyCouponButton(false);
						$('##couponCodeResponse').removeClass(responseContainerClasses).html('');

						var rateID = $('##mcsw_rateID').val();
						var objParams = { programType:'#arguments.programType#', couponCode:couponCode,
							memberID:#arguments.event.getValue('mid')#, rateID:rateID,
							rateAmtOverride:$('##override_swRatePrice').val() || '' };

						TS_AJX('ADMSWREG','validateCouponCode',objParams,validateResult,validateResult,10000,validateResult);
					} else {
						validateResult({ success:'false', couponresponse:'Invalid Promo Code' });
					}
				}
				function toggleApplyCouponButton(f){
					$('##btnApplyCouponCode').prop('disabled',!f);
					$('##btnApplyCouponCodeLabel').text(f?'Apply':'Applying...');
				}
				function recalculateDiscount(couponID){
					var getDiscountResult = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') {
							var defaultcurrencytype = $('b##MCTotalRegFee').data('defaultcurrencytype');
							var totalRegFee = getTotalRegFee();
							var discountAppliedTotal = totalRegFee - r.discount;
							var priceBox = '<span class="mcswreg_strike">$'+totalRegFee.toFixed(2)+defaultcurrencytype+'</span>&nbsp;&nbsp;<span class="mcswreg_highlight">$'+discountAppliedTotal.toFixed(2)+defaultcurrencytype+'</span><br/><span class="mcswreg_redtext">$'+r.discount+defaultcurrencytype+' discount applied</span>';

							$('##MCTotalRegFee').html(priceBox);
							$('##mcswreg_item_info').show();
							$('##mcswreg_couponbox').hide();
							$('##btnRemoveCoupon').parent().show();

							$('##mcsw_discount').val(r.discount);
						} else {
							alert('We were unable to recalculate the discount on price change.');
						}
					}

					var rateID = $('##mcsw_rateID').val();
					var objParams = { programType:'#arguments.programType#', couponID:couponID, 
						rateID:rateID, rateAmtOverride:$('##override_swRatePrice').val() || '' };
					TS_AJX('ADMSWREG','getRegistrationDiscount',objParams,getDiscountResult,getDiscountResult,10000,getDiscountResult);
				}

				function removeAppliedCoupon() {
					hideAppliedCouponInfo();
					$('##mcsw_couponID').val(0);
					$('##mcsw_discount').val(0);
					$('##mcswreg_couponbox').show();
				}
				function hideAppliedCouponInfo(){
					calculateTotalRegFee();
					$('##mcswreg_item_info').hide();
					$('##btnRemoveCoupon').parent().hide();
				}
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.regvalidate#">
	</cfif>
</div>	
</cfoutput>
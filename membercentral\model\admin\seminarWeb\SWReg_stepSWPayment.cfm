<cfsavecontent variable="local.SWRegPaymentJS">
	<cfoutput>
	<script type="text/javascript">
		var gridInitArray = new Array();
		gridInitArray["creditPaymentTab"] = false;
		gridInitArray["offlinePaymentTab"] = false;
	
		function onTabChangeHandler(ActiveTab) {
			switch(ActiveTab.id) {
				case "creditPaymentTab":
					$('##SWRegPaymentMethod').val('cc'); break;
				<cfif local.offerOnlinePaymentMethod>
					case "offlinePaymentTab":
						$('##SWRegPaymentMethod').val('offline');
						toggleSaveBtn(true); break;
				</cfif>
			}
		}
		
		$(function() {
			mca_initNavPills('SWRegPaymentTabs', 'creditPayment', false, onTabChangeHandler);
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.SWRegPaymentJS)#">

<cfoutput>
<div class="card card-box mt-2 mb-3">
	<div class="card-header bg-light">
		<div class="card-header--title font-weight-bold font-size-md">Registrant Summary</div>
		<div class="font-weight-bold">Totals</div>
	</div>
	<div class="card-body">
		<div class="row">
			<div class="col">#local.rateName#</div>
			<div class="col-auto">
				<cfif local.couponApplied>
					<span class="mcswreg_strike">#local.ratePriceDisplay#</span>&nbsp;&nbsp;
					<span class="mcswreg_highlight">#local.actualRatePriceDisplay#</span><br>
					<span class="mcswreg_redtext">#local.discountAppliedDisplay#</span><br/>
					<p id="mcswreg_item_info">#local.couponRedeemMessage#</p>
				<cfelse>
					#local.ratePriceDisplay#
				</cfif>
			</div>
		</div>
	</div>
</div>

<cfif local.totalRegFee eq 0 OR (local.couponApplied and local.actualTotalRegFee eq 0)>
	<div class="card card-box mt-2 mb-3">
		<div class="card-header bg-light">
			<div class="card-header--title font-weight-bold font-size-md">Payment Information</div>
		</div>
		<div class="card-body">
			<input type="hidden" name="SWRegPaymentMethod" id="SWRegPaymentMethod" value="cc">
			<div id="noPaymentDiv" class="font-weight-bold text-danger p-2">No Payment is due for this registration.</div>
			<div class="mt-3">
				<div>
					<button type="button" id="btnSubmitSave" class="btn btn-sm btn-primary appContinueBtn" onclick="submitSWRegForm();" style="width:180px;">
						<span class="btn-wrapper--icon"><i class="fa fa-circle-plus"></i></span>
						<span class="btn-wrapper--label">Save/Continue</span>
					</button>
					<span class="ml-2">Save registration</span>
				</div>
				<div class="mt-2">
					<button type="button" name="btnCancelReg" id="btnCancelReg" class="btn btn-sm btn-secondary" style="width:180px;">
						<span class="btn-wrapper--icon"><i class="fa fa-circle-minus"></i></span>
						<span class="btn-wrapper--label">Cancel Changes</span>
					</button>
					<span class="ml-2">Cancel this registration and return to list of registrants.</span>
				</div>
			</div>
		</div>
	</div>
<cfelse>
	<div class="card card-box mt-2 mb-3">
		<div class="card-header bg-light">
			<div class="card-header--title font-weight-bold font-size-md">Payment Information</div>
		</div>
		<div class="card-body p-0">
			<input type="hidden" name="SWRegPaymentMethod" id="SWRegPaymentMethod" value="cc">
			<div class="row no-gutters">
				<div class="col-3 bg-secondary py-5 px-2 border-right mr-3">
					<ul class="nav nav-pills nav-pills-hover flex-column" id="SWRegPaymentTabs">
						<cfset local.thisTabName = "creditPayment">
						<cfset local.thisTabID = "creditPaymentTab">	
						<li class="nav-item">
							<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
								<i class="fa-regular fa-credit-card"></i>
								Credit Card Payment
							</a>
						</li>
						<cfif local.offerOnlinePaymentMethod>
							<cfset local.thisTabName = "offlinePayment">
							<cfset local.thisTabID = "offlinePaymentTab">
							<li class="nav-item">
								<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
									<i class="fa-solid fa-money-check"></i>
									Offline Payment
								</a>
							</li>
						</cfif>
					</ul>
				</div>
				<div class="col p-4">
					<div class="tab-content p-2 pb-0" id="pills-tabContent">
						<div class="tab-pane fade" id="pills-creditPaymentTab" role="tabpanel" aria-labelledby="creditPaymentTab">
							<cfset local.strPaymentForm = CreateObject("component","model.system.platform.gateways.tsChargeCardCIM").gather(merchantOrgcode='SW', customerid=local.customerIDToUse, autoShowForm=1, editMode='controlPanelPayment')>
							<cfif len(local.strPaymentForm.headcode)>
								<cfhtmlhead text="#local.strPaymentForm.headcode#">
							</cfif>

							<cfif len(local.strPaymentForm.inputForm)>
								<div id="CIMTable" class="p-2">
									<div>#replaceNoCase(local.strPaymentForm.inputForm,'fld_','p_SW_fld_','ALL')#</div>
								</div>
								<div class="mt-2">
									<div>
										<button type="button" id="btnSubmitSave" class="btn btn-sm btn-primary appContinueBtn" onclick="submitSWRegForm();" style="width:180px;">
											<span class="btn-wrapper--icon"><i class="fa fa-circle-plus"></i></span>
											<span class="btn-wrapper--label">Save/Continue</span>
										</button>
										<span class="ml-2">Save registration</span>
									</div>
									<div class="mt-2">
										<button type="button" name="btnCancelReg" id="btnCancelReg" class="btn btn-sm btn-secondary" style="width:180px;">
											<span class="btn-wrapper--icon"><i class="fa fa-circle-minus"></i></span>
											<span class="btn-wrapper--label">Cancel Changes</span>
										</button>
										<span class="ml-2">Cancel this registration and return to list of registrants.</span>
									</div>
								</div>
							</cfif>

							<cfif len(local.strPaymentForm.jsvalidation)>
								<cfsavecontent variable="local.extrapayJS">
									<cfoutput>
									#replaceNoCase(local.strPaymentForm.jsvalidation,'fld_','p_SW_fld_','ALL')#
									</cfoutput>
								</cfsavecontent>
							</cfif>
						</div>
						<cfif local.offerOnlinePaymentMethod>
							<div class="tab-pane fade" id="pills-offlinePaymentTab" role="tabpanel" aria-labelledby="offlinePaymentTab">
								<div class="form-group">
									<div class="form-label-group">
										<input type="text" name="offlinePaymentDesc" id="offlinePaymentDesc" class="form-control" value="">
										<label for="offlinePaymentDesc">Payment Description</label>
									</div>
									<div class="form-text small text-dim">Check Number or Last 4 Digits of Card etc.</div>
								</div>
								<div class="form-group mt-4">
									<div class="form-label-group">
										<select name="tspaymentMethod" id="tspaymentMethod" class="form-control">
											<option value=""></option>
											<option value="B">ACH Direct Deposit</option>
											<option value="C">Check</option>
											<option value="A">Credit Card - American Express</option>
											<option value="D">Credit Card - Discover</option>
											<option value="M">Credit Card - Mastercard</option>
											<option value="V">Credit Card - Visa</option>
											<option value="I">Imported</option>
											<option value="L">Lockbox</option>
										</select>
										<label for="tspaymentMethod">Payment Method</label>
									</div>
								</div>
								<div class="mt-4">
									<div>
										<button type="button" id="btnSubmitSave" class="btn btn-sm btn-primary appContinueBtn" onclick="submitSWRegForm();" style="width:180px;">
											<span class="btn-wrapper--icon"><i class="fa fa-circle-plus"></i></span>
											<span class="btn-wrapper--label">Save/Continue</span>
										</button>
										<span class="ml-2">Save registration</span>
									</div>
									<div class="mt-2">
										<button type="button" name="btnCancelReg" id="btnCancelReg" class="btn btn-sm btn-secondary" style="width:180px;">
											<span class="btn-wrapper--icon"><i class="fa fa-circle-minus"></i></span>
											<span class="btn-wrapper--label">Cancel Changes</span>
										</button>
										<span class="ml-2">Cancel this registration and return to list of registrants.</span>
									</div>
								</div>
							</div>
						</cfif>
					</div>
				</div>
			</div>
		</div>
	</div>
</cfif>
</cfoutput>